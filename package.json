{"name": "bih-dicom-viewer", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@prisma/client": "^6.11.1", "bcryptjs": "^3.0.2", "cornerstone-core": "^2.6.1", "cornerstone-math": "^0.1.10", "cornerstone-tools": "^6.0.10", "cornerstone-wado-image-loader": "^4.13.2", "cornerstone-web-image-loader": "^2.1.1", "dicom-parser": "^1.8.21", "formidable": "^3.5.4", "fs-extra": "^11.3.0", "hammerjs": "^2.0.8", "jsonwebtoken": "^9.0.2", "next": "^15.3.4", "next-auth": "^4.24.11", "nodemailer": "^6.10.1", "prisma": "^6.11.1", "react": "^18.3.1", "react-dom": "^18.3.1", "yauzl": "^3.2.0"}, "devDependencies": {"autoprefixer": "^10.4.21", "eslint": "^8.57.0", "eslint-config-next": "^15.3.4", "postcss": "^8.5.6", "tailwindcss": "^3.4.17"}}