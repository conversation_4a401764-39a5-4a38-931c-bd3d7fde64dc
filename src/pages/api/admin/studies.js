import { requireAdminAuth } from '../../../lib/admin-auth-middleware';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { patient } = req.query;

    console.log(`📊 Admin fetching DICOM studies from database${patient ? ` for patient: ${patient}` : ' (all patients)'}`);

    // Build query conditions
    const whereConditions = {
      active: true // Only get active studies
    };

    // Filter by patient if specified
    if (patient) {
      whereConditions.uploadedPatientId = patient;
    }

    // Fetch studies from database with patient relationship
    const dbStudies = await prisma.dicomStudy.findMany({
      where: whereConditions,
      include: {
        patient: {
          select: {
            firstName: true,
            lastName: true,
            urn: true
          }
        }
      },
      orderBy: [
        { studyDate: 'desc' },
        { studyTime: 'desc' },
        { createdAt: 'desc' }
      ]
    });

    console.log(`📚 Found ${dbStudies.length} studies in database`);

    // Transform database results to match the expected format
    const studies = {};

    for (const dbStudy of dbStudies) {
      // Create study object in the expected format
      const study = {
        studyInstanceUID: dbStudy.studyInstanceUID,
        patientName: dbStudy.patientName,
        patientID: dbStudy.patientID,
        studyDate: dbStudy.studyDate,
        studyTime: dbStudy.studyTime,
        studyDescription: dbStudy.studyDescription,
        modality: dbStudy.modality,
        thumbnail: dbStudy.thumbnail,
        firstFile: dbStudy.firstFile,
        uploadedPatientId: dbStudy.uploadedPatientId,
        uploadedFolderName: dbStudy.uploadedFolderName,

        // Add patient information from database relationship
        uploadedPatientName: dbStudy.patient
          ? `${dbStudy.patient.firstName} ${dbStudy.patient.lastName}`
          : 'Unknown Patient',

        // Add metadata for compatibility
        files: [], // Will be populated when needed by individual study endpoints
        series: {} // Will be populated when needed by individual study endpoints
      };

      // Use studyInstanceUID as key (same as before)
      studies[dbStudy.studyInstanceUID] = study;
    }

    await prisma.$disconnect();

    res.status(200).json({
      studies,
      patientFilter: patient || null,
      message: `Loaded ${dbStudies.length} studies from database${patient ? ` for patient ${patient}` : ' (all patients)'}`,
      source: 'database'
    });

  } catch (error) {
    console.error('Error fetching DICOM studies from database:', error);
    await prisma.$disconnect();
    res.status(500).json({
      error: 'Error loading DICOM studies from database',
      message: error.message
    });
  }
}

export default requireAdminAuth(handler);
