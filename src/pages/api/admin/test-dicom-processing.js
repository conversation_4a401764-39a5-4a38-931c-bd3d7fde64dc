import { requireAdminAuth } from '../../../lib/auth-middleware';
import { PrismaClient } from '@prisma/client';
import { getDicomFiles, organizeDicomStudies } from '../../../lib/dicom';

async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const { folderName } = req.body;

  if (!folderName) {
    return res.status(400).json({ error: 'folderName is required' });
  }

  let prismaClient = null;

  try {
    console.log(`🧪 Testing DICOM processing for folder: ${folderName}`);
    
    // Create a new Prisma client instance
    prismaClient = new PrismaClient();

    // Get DICOM files from the folder
    console.log(`🔍 Looking for DICOM files in folder: ${folderName}`);
    const files = getDicomFiles(folderName);
    console.log(`📁 Found ${files.length} DICOM files:`, files.slice(0, 5));
    
    if (files.length === 0) {
      return res.status(200).json({
        success: false,
        message: `No DICOM files found in folder: ${folderName}`,
        files: [],
        studies: []
      });
    }

    // Organize files into studies
    console.log(`🔄 Organizing ${files.length} files into studies...`);
    const studies = organizeDicomStudies(files);
    const studyEntries = Object.entries(studies);
    
    console.log(`📚 Found ${studyEntries.length} DICOM studies`);

    let studiesProcessed = 0;
    let studiesSkipped = 0;
    const processedStudies = [];

    // Extract patient ID from folder name
    const uploadedPatientId = folderName.split('_')[0];
    console.log(`👤 Extracted patient ID: ${uploadedPatientId}`);

    for (const [studyInstanceUID, study] of studyEntries) {
      try {
        console.log(`🔍 Processing study: ${studyInstanceUID}`);

        // Check if study already exists
        const existingStudy = await prismaClient.dicomStudy.findUnique({
          where: { studyInstanceUID }
        });

        if (existingStudy) {
          console.log(`⏭️ Study already exists: ${studyInstanceUID}`);
          studiesSkipped++;
          processedStudies.push({
            studyInstanceUID,
            status: 'skipped',
            reason: 'already exists',
            existingId: existingStudy.id
          });
          continue;
        }

        // Prepare study data
        const studyData = {
          studyInstanceUID,
          patientName: study.patientName || null,
          patientID: study.patientID || null,
          studyDate: study.studyDate || null,
          studyTime: study.studyTime || null,
          studyDescription: study.studyDescription || null,
          modality: study.modality || null,
          thumbnail: study.thumbnail || null,
          firstFile: study.firstFile,
          uploadedPatientId,
          uploadedFolderName: folderName,
          active: true
        };

        console.log(`💾 Inserting study:`, studyData);

        // Insert study into database
        const createdStudy = await prismaClient.dicomStudy.create({
          data: studyData
        });

        console.log(`✅ Created study with ID: ${createdStudy.id}`);
        studiesProcessed++;
        
        processedStudies.push({
          studyInstanceUID,
          status: 'created',
          databaseId: createdStudy.id,
          studyData
        });

      } catch (studyError) {
        console.error(`❌ Error processing study ${studyInstanceUID}:`, studyError);
        studiesSkipped++;
        
        processedStudies.push({
          studyInstanceUID,
          status: 'error',
          error: studyError.message
        });
      }
    }

    res.status(200).json({
      success: true,
      folderName,
      uploadedPatientId,
      filesFound: files.length,
      studiesFound: studyEntries.length,
      studiesProcessed,
      studiesSkipped,
      processedStudies,
      sampleFiles: files.slice(0, 5),
      sampleStudies: studyEntries.slice(0, 2).map(([uid, study]) => ({
        studyInstanceUID: uid,
        patientName: study.patientName,
        patientID: study.patientID,
        studyDate: study.studyDate,
        modality: study.modality,
        firstFile: study.firstFile
      }))
    });

  } catch (error) {
    console.error('❌ Test processing error:', error);
    res.status(500).json({
      success: false,
      error: error.message,
      stack: error.stack
    });
  } finally {
    if (prismaClient) {
      await prismaClient.$disconnect();
    }
  }
}

export default requireAdminAuth(handler);
