/* File Browser styles for DICOM Viewer */

.file-browser {
  width: 300px;
  background: #2d2d2d;
  border-right: 1px solid #444;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.file-browser-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background: #333;
  border-bottom: 1px solid #444;
}

.file-browser-header h3 {
  margin: 0;
  color: white;
  font-size: 14px;
}

.close-btn {
  background: none;
  border: none;
  color: #ccc;
  cursor: pointer;
  font-size: 16px;
  padding: 5px;
  border-radius: 3px;
  transition: all 0.2s;
}

.close-btn:hover {
  background: #555;
  color: white;
}

.file-list {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
}

.file-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  margin-bottom: 10px;
  border: 2px solid transparent;
  min-height: 200px;
  position: relative;
}

.file-item:hover {
  background: #444;
  border-color: #666;
}

.file-item.active {
  background: #007bff;
  border-color: #0056b3;
}

.file-thumbnail {
  margin-bottom: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.file-info {
  text-align: center;
  width: 100%;
}

.file-name {
  color: white;
  font-size: 12px;
  font-weight: 500;
  margin-bottom: 4px;
  word-break: break-all;
  line-height: 1.2;
}

.file-size {
  color: #999;
  font-size: 10px;
}

.current-indicator {
  position: absolute;
  top: 8px;
  right: 8px;
  font-size: 16px;
  background: rgba(0, 123, 255, 0.8);
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading,
.error,
.empty {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  color: #ccc;
  text-align: center;
}

.error {
  flex-direction: column;
  gap: 10px;
  color: #ff6b6b;
}

.error button {
  background: #ff6b6b;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.error button:hover {
  background: #ff5252;
}

/* Scrollbar styling */
.file-list::-webkit-scrollbar {
  width: 6px;
}

.file-list::-webkit-scrollbar-track {
  background: #333;
}

.file-list::-webkit-scrollbar-thumb {
  background: #666;
  border-radius: 3px;
}

.file-list::-webkit-scrollbar-thumb:hover {
  background: #777;
}

@media (max-width: 768px) {
  .file-browser {
    width: 250px;
  }
}