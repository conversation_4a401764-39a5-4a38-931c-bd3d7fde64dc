.folder-upload {
  max-width: 600px;
  margin: 20px auto;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.upload-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.patient-id-input {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.patient-id-input label {
  font-weight: 600;
  color: #333;
  font-size: 14px;
}

.patient-id-input input {
  padding: 12px;
  border: 2px solid #ddd;
  border-radius: 6px;
  font-size: 16px;
  transition: border-color 0.2s;
}

.patient-id-input input:focus {
  outline: none;
  border-color: #007bff;
}

.patient-id-input input:disabled {
  background-color: #f5f5f5;
  cursor: not-allowed;
}

.drop-zone {
  border: 3px dashed #ddd;
  border-radius: 8px;
  padding: 40px 20px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: #fafafa;
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.drop-zone:hover {
  border-color: #007bff;
  background-color: #f0f8ff;
}

.drop-zone.dragging {
  border-color: #007bff;
  background-color: #e6f3ff;
  transform: scale(1.02);
}

.drop-zone.uploading {
  border-color: #28a745;
  background-color: #f0fff0;
  cursor: not-allowed;
}

.drop-zone-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.upload-icon {
  font-size: 48px;
  opacity: 0.6;
}

.drop-zone h3 {
  margin: 0;
  color: #333;
  font-size: 18px;
  font-weight: 600;
}

.drop-zone p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.drop-zone small {
  color: #999;
  font-size: 12px;
}

.upload-status {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.upload-status p {
  margin: 0;
  color: #333;
  font-weight: 500;
}

.upload-results {
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #28a745;
}

.upload-results p {
  margin: 4px 0;
  font-size: 14px;
}

.upload-results p:first-child {
  font-weight: 600;
  color: #28a745;
}

/* Responsive design */
@media (max-width: 768px) {
  .folder-upload {
    margin: 10px;
    padding: 15px;
  }

  .drop-zone {
    padding: 30px 15px;
    min-height: 150px;
  }

  .upload-icon {
    font-size: 36px;
  }

  .drop-zone h3 {
    font-size: 16px;
  }
}

/* Progress bars for chunked uploads */
.progress-details {
  margin-top: 1rem;
  text-align: center;
}

.current-file {
  font-size: 0.9rem;
  color: #6b7280;
  margin: 0.5rem 0;
  word-break: break-all;
}

.progress-bar {
  position: relative;
  width: 100%;
  height: 20px;
  background-color: #e5e7eb;
  border-radius: 10px;
  margin: 0.5rem 0;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #3b82f6, #1d4ed8);
  border-radius: 10px;
  transition: width 0.3s ease;
}

.progress-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 0.8rem;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.chunk-progress {
  margin-top: 0.5rem;
  padding: 0.5rem;
  background-color: #f9fafb;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
}

.chunk-info {
  font-size: 0.8rem;
  color: #6b7280;
  margin: 0 0 0.5rem 0;
}

.chunk-progress-bar {
  position: relative;
  width: 100%;
  height: 16px;
  background-color: #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
}

.chunk-progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #10b981, #059669);
  border-radius: 8px;
  transition: width 0.2s ease;
}

.chunk-progress-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 0.7rem;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}