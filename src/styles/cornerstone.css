/* Cornerstone Viewer styles */

.cornerstone-container {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 80px);
  background: #000;
}

.viewer-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.dicom-viewport {
  flex: 1;
  position: relative;
  background: #000;
}

.cornerstone-element {
  width: 100%;
  height: 100%;
  background: #000;
}

.frame-info {
  position: absolute;
  top: 10px;
  left: 10px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 14px;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #333;
  border-top: 4px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

.loading-text {
  color: white;
  font-size: 16px;
  text-align: center;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}