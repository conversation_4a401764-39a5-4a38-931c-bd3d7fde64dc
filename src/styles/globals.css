@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom base styles */
@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }

  body {
    @apply bg-gray-50 text-gray-900 m-0 p-5;
  }
}

/* Custom component styles */
@layer components {
  .layout {
    @apply max-w-7xl mx-auto;
  }

  .header {
    @apply text-center mb-8 p-5 bg-gradient-to-br from-blue-500 to-purple-600 text-white rounded-xl shadow-lg;
  }

  .header h1 {
    @apply m-0 mb-3 text-4xl font-bold;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  }

  .header p {
    @apply my-1 text-lg opacity-90;
  }

  .btn-primary {
    @apply bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
  }

  .btn-secondary {
    @apply bg-gray-200 hover:bg-gray-300 text-gray-800 font-medium py-2 px-4 rounded-lg transition-colors duration-200;
  }

  .card {
    @apply bg-white rounded-lg shadow-md border border-gray-200;
  }

  .input-field {
    @apply w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent;
  }

  .medical-card {
    @apply bg-white rounded-xl shadow-lg border border-gray-100 hover:shadow-xl transition-shadow duration-300;
  }

  .header-actions {
    @apply flex justify-center items-center gap-5 my-5 flex-wrap;
  }

  .upload-button {
    @apply bg-green-600 hover:bg-green-700 text-white no-underline py-3 px-6 rounded-lg font-semibold text-sm transition-all duration-300 shadow-lg hover:-translate-y-0.5;
  }

  .patient-filter {
    @apply flex items-center gap-2 bg-white bg-opacity-10 py-2 px-4 rounded-lg backdrop-blur-sm;
  }

  .patient-filter label {
    @apply text-sm font-medium whitespace-nowrap;
  }

  .patient-filter select {
    @apply py-1.5 px-3 border border-white border-opacity-30 rounded bg-white bg-opacity-90 text-gray-800 text-sm min-w-[150px] focus:outline-none focus:border-green-500 focus:ring-2 focus:ring-green-200;
  }

  .auth-actions {
    @apply flex items-center gap-4;
  }

  .user-info {
    @apply flex items-center gap-3 bg-white bg-opacity-10 py-2 px-4 rounded-lg backdrop-blur-sm;
  }

  .welcome-text {
    @apply text-white text-sm font-medium;
  }

  .logout-button {
    @apply bg-red-600 hover:bg-red-700 text-white border-none py-1.5 px-3 rounded text-xs font-medium cursor-pointer transition-colors duration-200;
  }

  .auth-links {
    @apply flex gap-3;
  }

  .auth-link {
    @apply text-white no-underline py-2 px-4 rounded text-sm font-medium transition-all duration-200 border border-white border-opacity-30 hover:bg-white hover:bg-opacity-10 hover:no-underline;
  }

  .auth-link.register {
    @apply bg-white bg-opacity-20 border-white border-opacity-50 hover:bg-opacity-30;
  }

  .container {
    @apply max-w-6xl mx-auto;
  }

  /* Gallery and Study Styles */
  .image-grid {
    @apply grid grid-cols-[repeat(auto-fill, minmax(300px, 1fr))] gap-5 mt-5;
  }

  .image-card {
    @apply bg-white rounded-lg shadow-md p-5 transition-transform duration-200 hover:-translate-y-0.5 hover:shadow-xl;
  }

  .image-info {
    @apply mb-4;
  }

  .info-row {
    @apply flex justify-between mb-2 py-1 border-b border-gray-200;
  }

  .info-label {
    @apply font-bold text-gray-600 min-w-[120px];
  }

  .info-value {
    @apply text-gray-800 text-right flex-1;
  }

  .view-button {
    @apply bg-gradient-to-br from-blue-500 to-purple-600 text-white border-none py-3 px-6 rounded-lg cursor-pointer text-base font-bold w-full transition-all duration-300 no-underline inline-block text-center hover:-translate-y-0.5 hover:shadow-2xl;
  }

  .study-section {
    @apply mb-10 bg-white rounded-xl shadow-lg overflow-hidden;
  }

  .study-header {
    @apply bg-gradient-to-br from-blue-500 to-purple-600 text-white p-5 m-0;
  }

  .study-info {
    @apply p-5 bg-gray-50 border-b border-gray-300;
  }

  .series-grid {
    @apply grid grid-cols-[repeat(auto-fill, minmax(280px, 1fr))] gap-5 p-5;
  }

  .series-card {
    @apply bg-white border border-gray-300 rounded-lg overflow-hidden transition-all duration-300 hover:-translate-y-1 hover:shadow-2xl;
  }

  .series-header {
    @apply bg-gray-200 p-4 border-b border-gray-300;
  }

  .series-title {
    @apply m-0 text-gray-700 text-base;
  }

  .series-info {
    @apply p-4;
  }

  .instances-list {
    @apply max-h-[200px] overflow-y-auto mt-2;
  }

  .instance-item {
    @apply flex justify-between items-center py-2 border-b border-gray-100 last:border-b-0;
  }

  .instance-filename {
    @apply font-mono text-xs text-gray-500;
  }

  .instance-number {
    @apply bg-blue-600 text-white py-0.5 px-2 rounded-full text-xs font-bold;
  }
}

/* Custom utility styles */
@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}