/* DICOM Viewer styles */

.viewer-container {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #1a1a1a;
  color: white;
}

.viewer-header {
  background: #2d2d2d;
  padding: 15px 20px;
  border-bottom: 1px solid #444;
}

.viewer-header h2 {
  margin: 0 0 10px 0;
  color: #fff;
}

.metadata-summary {
  display: flex;
  gap: 20px;
  font-size: 14px;
  color: #ccc;
}

.metadata-summary span {
  background: #444;
  padding: 4px 8px;
  border-radius: 4px;
}

.loading,
.error {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  font-size: 18px;
}

.error {
  color: #ff6b6b;
}

.dicom-viewport {
  position: relative;
  flex: 1;
  overflow: hidden;
}

.frame-scroll-bar:hover {
  background-color: rgba(0, 0, 0, 0.9) !important;
}

.frame-scroll-bar:hover div:first-child {
  background-color: #0099ff !important;
}

.btn {
  background: #007bff;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s;
}

.btn:hover {
  background: #0056b3;
}

.btn-primary {
  background: #007bff;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s;
}

.btn-primary:hover {
  background: #0056b3;
}