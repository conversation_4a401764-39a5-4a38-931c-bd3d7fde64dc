// Quick test to verify the studies API is working with real data
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testStudiesAPI() {
  try {
    console.log('🧪 Testing DicomStudy database queries...');

    // Test 1: Count total studies
    const totalStudies = await prisma.dicomStudy.count({
      where: { active: true }
    });
    console.log(`📊 Total active studies: ${totalStudies}`);

    // Test 2: Get studies with pagination (like the API does)
    const paginatedStudies = await prisma.dicomStudy.findMany({
      where: { active: true },
      include: {
        patient: {
          select: {
            firstName: true,
            lastName: true,
            urn: true
          }
        }
      },
      orderBy: [
        { createdAt: 'desc' }
      ],
      skip: 0,
      take: 5
    });

    console.log(`\n📋 First 5 studies (paginated):`);
    paginatedStudies.forEach((study, index) => {
      console.log(`   ${index + 1}. ${study.studyInstanceUID}`);
      console.log(`      Patient: ${study.patientName} (ID: ${study.uploadedPatientId})`);
      console.log(`      Modality: ${study.modality}, Date: ${study.studyDate}`);
      console.log(`      Folder: ${study.uploadedFolderName}`);
      console.log(`      DB Patient: ${study.patient ? `${study.patient.firstName} ${study.patient.lastName}` : 'Not found'}`);
      console.log('');
    });

    // Test 3: Search functionality
    const searchResults = await prisma.dicomStudy.findMany({
      where: {
        active: true,
        OR: [
          { patientName: { contains: 'Oliver' } },
          { uploadedPatientId: { contains: '000012' } }
        ]
      },
      take: 3
    });

    console.log(`🔍 Search results for 'Oliver' or patient '000012': ${searchResults.length} found`);
    searchResults.forEach((study, index) => {
      console.log(`   ${index + 1}. ${study.patientName} (${study.uploadedPatientId}) - ${study.modality}`);
    });

    // Test 4: Patient filter
    const patientStudies = await prisma.dicomStudy.findMany({
      where: {
        active: true,
        uploadedPatientId: '002711'
      }
    });

    console.log(`\n👤 Studies for patient 002711: ${patientStudies.length} found`);
    patientStudies.forEach((study, index) => {
      console.log(`   ${index + 1}. ${study.studyDescription} - ${study.modality} (${study.studyDate})`);
    });

    // Test 5: Modality distribution
    const modalityStats = await prisma.dicomStudy.groupBy({
      by: ['modality'],
      where: { active: true },
      _count: {
        modality: true
      },
      orderBy: {
        _count: {
          modality: 'desc'
        }
      }
    });

    console.log(`\n📈 Modality distribution:`);
    modalityStats.forEach((stat) => {
      console.log(`   ${stat.modality}: ${stat._count.modality} studies`);
    });

    console.log('\n✅ All tests completed successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error);
    console.error('Stack trace:', error.stack);
  } finally {
    await prisma.$disconnect();
  }
}

testStudiesAPI();
