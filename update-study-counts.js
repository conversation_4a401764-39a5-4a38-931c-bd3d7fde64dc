// Script to update existing DicomStudy records with totalFiles and totalSeries counts
const { PrismaClient } = require('@prisma/client');
const fs = require('fs');

const prisma = new PrismaClient();

async function updateStudyCounts() {
  try {
    console.log('🔄 Starting to update study counts...');

    // Read the JSON file
    const jsonData = fs.readFileSync('real.json', 'utf8');
    const data = JSON.parse(jsonData);

    const studies = data.studies;
    const studyEntries = Object.entries(studies);

    console.log(`📊 Found ${studyEntries.length} studies to update`);

    let updated = 0;
    let notFound = 0;
    let errors = 0;

    for (const [studyInstanceUID, study] of studyEntries) {
      try {
        // Calculate counts from the JSON data
        const totalFiles = study.files ? study.files.length : 0;
        const totalSeries = study.series ? Object.keys(study.series).length : 0;

        console.log(`📋 Study ${studyInstanceUID}: ${totalFiles} files, ${totalSeries} series`);

        // Update the study in database
        const updateResult = await prisma.dicomStudy.updateMany({
          where: { studyInstanceUID },
          data: {
            totalFiles,
            totalSeries
          }
        });

        if (updateResult.count > 0) {
          updated++;
          console.log(`✅ Updated study ${updated}/${studyEntries.length}: ${studyInstanceUID}`);
        } else {
          notFound++;
          console.log(`⚠️ Study not found in database: ${studyInstanceUID}`);
        }

        // Progress update every 20 studies
        if ((updated + notFound + errors) % 20 === 0) {
          console.log(`📈 Progress: ${updated} updated, ${notFound} not found, ${errors} errors`);
        }

      } catch (studyError) {
        console.error(`❌ Error updating study ${studyInstanceUID}:`, studyError.message);
        errors++;
      }
    }

    console.log('\n🎉 Study count update completed!');
    console.log(`📊 Final Results:`);
    console.log(`   ✅ Updated: ${updated} studies`);
    console.log(`   ⚠️ Not found: ${notFound} studies`);
    console.log(`   ❌ Errors: ${errors} studies`);
    console.log(`   📈 Total processed: ${updated + notFound + errors}/${studyEntries.length}`);

    // Verify updates
    const studiesWithCounts = await prisma.dicomStudy.count({
      where: {
        AND: [
          { totalFiles: { not: null } },
          { totalSeries: { not: null } }
        ]
      }
    });

    console.log(`\n🗄️ Studies with count data: ${studiesWithCounts}`);

    // Show some sample data with counts
    const sampleStudies = await prisma.dicomStudy.findMany({
      where: {
        AND: [
          { totalFiles: { not: null } },
          { totalSeries: { not: null } }
        ]
      },
      take: 5,
      orderBy: { totalFiles: 'desc' },
      select: {
        id: true,
        studyInstanceUID: true,
        patientName: true,
        uploadedPatientId: true,
        modality: true,
        totalFiles: true,
        totalSeries: true
      }
    });

    console.log('\n📋 Sample studies with highest file counts:');
    sampleStudies.forEach((study, index) => {
      console.log(`   ${index + 1}. Patient: ${study.uploadedPatientId}, Modality: ${study.modality}`);
      console.log(`      Files: ${study.totalFiles}, Series: ${study.totalSeries}`);
    });

    // Statistics
    const stats = await prisma.dicomStudy.aggregate({
      where: {
        AND: [
          { totalFiles: { not: null } },
          { totalSeries: { not: null } }
        ]
      },
      _avg: {
        totalFiles: true,
        totalSeries: true
      },
      _max: {
        totalFiles: true,
        totalSeries: true
      },
      _min: {
        totalFiles: true,
        totalSeries: true
      }
    });

    console.log('\n📈 Statistics:');
    console.log(`   Average files per study: ${Math.round(stats._avg.totalFiles || 0)}`);
    console.log(`   Average series per study: ${Math.round(stats._avg.totalSeries || 0)}`);
    console.log(`   Max files in a study: ${stats._max.totalFiles || 0}`);
    console.log(`   Max series in a study: ${stats._max.totalSeries || 0}`);
    console.log(`   Min files in a study: ${stats._min.totalFiles || 0}`);
    console.log(`   Min series in a study: ${stats._min.totalSeries || 0}`);

  } catch (error) {
    console.error('💥 Fatal error during count update:', error);
    console.error('Stack trace:', error.stack);
  } finally {
    await prisma.$disconnect();
    console.log('\n🔌 Database connection closed');
  }
}

// Run the update
updateStudyCounts();
