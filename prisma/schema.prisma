// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider     = "mysql"
  url          = env("DATABASE_URL")
  relationMode = "prisma"
}

// OTP model for email authentication
model Otp {
  id              Int      @id @default(autoincrement())
  email           String   @db.VarChar(255)
  sessionId       String   @unique @map("session_id") @db.VarChar(255)
  otp             String   @db.VarChar(10)
  createdAt       DateTime @default(now()) @map("created_at")
  expiresAt       DateTime @map("expires_at")
  verified        <PERSON><PERSON><PERSON>  @default(false)
  retryCount      Int      @default(1) @map("retry_count")
  attempts        Int      @default(0)
  lastRequestTime DateTime @default(now()) @map("last_request_time")

  @@index([email])
  @@index([sessionId])
  @@index([expiresAt])
  @@map("otps")
}

// Patient model for patient authentication and file access (existing table)
model Patient {
  idPatients BigInt     @id @default(autoincrement()) @map("id_patients") @db.UnsignedBigInt
  urn        String     @unique @db.VarChar(10)
  psid       String     @unique @db.VarChar(10)
  lastName   String     @map("last_name") @db.VarChar(255)
  firstName  String     @map("first_name") @db.VarChar(255)
  email      String?    @db.VarChar(255)
  sex        PatientSex
  age        Int
  dob        String     @db.VarChar(255)
  createdAt  DateTime?  @default(now()) @map("created_at")
  updatedAt  DateTime?  @updatedAt @map("updated_at")

  // Relationship to DICOM studies
  dicomStudies DicomStudy[]

  @@index([email])
  @@index([psid])
  @@index([urn])
  @@map("patients")
}

enum PatientSex {
  male
  female
}

// DICOM Study model for storing study metadata
model DicomStudy {
  id                 Int      @id @default(autoincrement())
  studyInstanceUID   String   @unique @map("study_instance_uid") @db.VarChar(255)
  patientName        String?  @map("patient_name") @db.VarChar(255)
  patientID          String?  @map("patient_id") @db.VarChar(255)
  studyDate          String?  @map("study_date") @db.VarChar(20)
  studyTime          String?  @map("study_time") @db.VarChar(20)
  studyDescription   String?  @map("study_description") @db.VarChar(500)
  modality           String?  @db.VarChar(10)
  thumbnail          String?  @db.LongText // Base64 encoded thumbnail
  firstFile          String   @map("first_file") @db.VarChar(500)
  uploadedPatientId  String   @map("uploaded_patient_id") @db.VarChar(10)
  uploadedFolderName String   @map("uploaded_folder_name") @db.VarChar(255)
  active             Boolean  @default(true)
  createdAt          DateTime @default(now()) @map("created_at")
  updatedAt          DateTime @updatedAt @map("updated_at")

  // Foreign key relationship to Patient
  patient Patient? @relation(fields: [uploadedPatientId], references: [urn])

  @@index([studyInstanceUID])
  @@index([uploadedPatientId])
  @@index([uploadedFolderName])
  @@index([studyDate])
  @@index([modality])
  @@index([active])
  @@map("dicom_studies")
}

// User model for admin portal authentication (existing table)
model User {
  id              BigInt    @id @default(autoincrement()) @db.UnsignedBigInt
  name            String    @db.VarChar(255)
  email           String    @unique @db.VarChar(255)
  role            UserRole  @default(backoffice)
  emailVerifiedAt DateTime? @map("email_verified_at")
  password        String    @db.VarChar(255)
  rememberToken   String?   @map("remember_token") @db.VarChar(100)
  createdAt       DateTime? @default(now()) @map("created_at")
  updatedAt       DateTime? @updatedAt @map("updated_at")

  @@map("users")
}

enum UserRole {
  superadmin
  backoffice
  kitchen
}
